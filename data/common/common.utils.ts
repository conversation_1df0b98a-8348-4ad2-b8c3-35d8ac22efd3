import { DATE_FORMAT } from "@/constants/date-formats";
import { format, parse, parseISO } from "date-fns";

export const formatDate = (date?: string | Date | undefined | null) => {
  if (date && typeof date === "object" && date instanceof Date) {
    return format(parseISO(date.toISOString()), DATE_FORMAT.YEAR_MONTH_DAY);
  }

  const isoDate = date ? parseISO(date) : parseISO(new Date().toISOString());
  return format(isoDate, DATE_FORMAT.YEAR_MONTH_DAY);
};
export const getInitials = (name: string) => {
  const words = name.split(" ");
  return words
    .map((word) => word.charAt(0))
    .join("")
    .toUpperCase();
};

export const formatTime = (time: string) => {
  if (!time) {
    return "-";
  }

  const splittedTime = time.split(" ");

  const timeSplitted = splittedTime[1] ?? splittedTime[0] ?? "";

  const parsedTime = parse(timeSplitted, "HH:mm:ss", new Date());

  // To allow no space between time and am/pm
  return format(parsedTime, "h:mma");
};

export const obtainDateFrame = (startTime: string, endTime: string) => {
  if (startTime?.length === 5) {
    startTime += ":00";
  }

  if (endTime?.length === 5) {
    endTime += ":00";
  }
  return `${formatTime(startTime)} - ${formatTime(endTime)}`;
};

export const formatHourMinsTime = (time: string) => {
  const parsedTime = parse(time, "HH:mm:ss", new Date());
  return format(parsedTime, "HH:MM");
};
